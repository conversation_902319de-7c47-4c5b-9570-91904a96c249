diff --git a/.env.development b/.env.development
index 483b5b7..9e9f0a8 100644
--- a/.env.development
+++ b/.env.development
@@ -1 +1 @@
-VITE_API_BASE_URL=https://india-customer-care-api.apps.openxcell.dev/cms/v1
\ No newline at end of file
+# VITE_API_BASE_URL=https://india-customer-care-api.apps.openxcell.dev/cms/v1
\ No newline at end of file
diff --git a/src/components/editor/text-editor.css b/src/components/editor/text-editor.css
new file mode 100644
index 0000000..6ee8ddc
--- /dev/null
+++ b/src/components/editor/text-editor.css
@@ -0,0 +1,11 @@
+/* .jodit-box { 
+    position: relative !important;
+    transform: translate(-470px, -90px);
+} */
+
+#jodit-editor {
+  position: static;
+  transform: translate(0, 0);
+  margin: 0 auto;
+  box-sizing: border-box;
+}
diff --git a/src/components/editor/text-editor.jsx b/src/components/editor/text-editor.jsx
index 9f8e59a..fcaea30 100644
--- a/src/components/editor/text-editor.jsx
+++ b/src/components/editor/text-editor.jsx
@@ -1,31 +1,9 @@
 import React, { useEffect, useState } from 'react';
 import 'jodit';
-import 'jodit/es2015/jodit.min.css';
+import 'jodit/es2021/jodit.min.css';
+import './text-editor.css';
 import JoditEditor from 'jodit-react';
 
-const copyStringToClipboard = function (str) {
-  var el = document.createElement('textarea');
-  el.value = str;
-  el.setAttribute('readonly', '');
-  el.style = { position: 'absolute', left: '-9999px' };
-  document.body.appendChild(el);
-  el.select();
-  document.execCommand('copy');
-  document.body.removeChild(el);
-};
-
-const facilityMergeFields = [
-  'FacilityNumber',
-  'FacilityName',
-  'Address',
-  'MapCategory',
-  'Latitude',
-  'Longitude',
-  'ReceivingPlant',
-  'TrunkLine',
-  'SiteElevation',
-];
-
 const buttons = [
   'undo',
   'redo',
@@ -58,30 +36,29 @@ const buttons = [
   'eraser',
   'copyformat',
   '|',
-  'fullsize',
-  'selectall',
-  'print',
-  '|',
 ];
 
 const editorConfig = {
   readonly: false,
+  // container: document.getElementById('root'),
+  iframe: true,
   toolbar: true,
   spellcheck: true,
   language: 'en',
   toolbarButtonSize: 'medium',
-  toolbarAdaptive: false,
-  showCharsCounter: true,
-  showWordsCounter: true,
+  toolbarAdaptive: true,
+  showCharsCounter: false,
+  showWordsCounter: false,
   showXPathInStatusbar: false,
   askBeforePasteHTML: true,
   askBeforePasteFromWord: true,
-  defaultActionOnPaste: "insert_clear_html",
+  defaultActionOnPaste: 'insert_clear_html',
   buttons: buttons,
   uploader: {
     insertImageAsBase64URI: true,
   },
   height: 500,
+  allowResizeY: false,
 };
 
 function Editor({ initialContent = '', onContentChange }) {
@@ -102,16 +79,12 @@ function Editor({ initialContent = '', onContentChange }) {
   };
 
   return (
-    <div style={{
-      width: '100%',
-      display: 'flex',
-      flexDirection: 'column'
-    }}>
+    <div id="jodit-editor">
       <JoditEditor
         style={{
           flex: 1,
           width: '100%',
-          margin: '0 auto'
+          margin: '0 auto',
         }}
         value={data}
         config={editorConfig}
diff --git a/src/components/static-content/static-page-dialog.jsx b/src/components/static-content/static-page-dialog.jsx
index 52b28d9..70991e7 100644
--- a/src/components/static-content/static-page-dialog.jsx
+++ b/src/components/static-content/static-page-dialog.jsx
@@ -1,83 +1,103 @@
-import React, { useState, useEffect } from "react";
+import React, { useState, useEffect } from 'react';
 import {
   Dialog,
   DialogContent,
   DialogHeader,
   DialogTitle,
   DialogFooter,
-} from "@/components/ui/dialog";
-import { Input } from "@/components/ui/input";
-import { Label } from "@/components/ui/label";
-import { Button } from "@/components/ui/button";
-import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
-import { AlertCircle } from "lucide-react";
-import { toast } from "sonner";
-import Editor from "../editor/text-editor";
-import { useCreateStaticPage, useUpdateStaticPage } from "@/hooks/api/static-content.api";
+} from '@/components/ui/dialog';
+import { Input } from '@/components/ui/input';
+import { Label } from '@/components/ui/label';
+import { Button } from '@/components/ui/button';
+import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
+import { AlertCircle } from 'lucide-react';
+import { toast } from 'sonner';
+import Editor from '../editor/text-editor';
+import {
+  useCreateStaticPage,
+  useUpdateStaticPage,
+} from '@/hooks/api/static-content.api';
 
 // Function to generate slug from title
 const generateSlug = (title) => {
   return title
     .toLowerCase()
     .replace(/[^\w\s-]/g, '') // Remove special characters
-    .replace(/\s+/g, '-')     // Replace spaces with hyphens
-    .replace(/-+/g, '-')      // Replace multiple hyphens with single hyphen
-    .trim();                  // Trim leading/trailing spaces
+    .replace(/\s+/g, '-') // Replace spaces with hyphens
+    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
+    .trim(); // Trim leading/trailing spaces
 };
 
 export function StaticPageDialog({ open, onOpenChange, page, onSave }) {
   const [formData, setFormData] = useState({
-    title: "",
-    slug: "",
-    content: "",
+    title: '',
+    slug: '',
+    content: '',
   });
   const [errors, setErrors] = useState({});
   const [isSubmitting, setIsSubmitting] = useState(false);
+  // Add a key state to force re-render of the Editor component
+  const [editorKey, setEditorKey] = useState(Date.now());
 
   // Use a single hook for both create and update operations
-  const { createPage, loading: createLoading, error: createError } = useCreateStaticPage();
-  const { updatePage: updatePageFunc, loading: updateLoading, error: updateError } = useUpdateStaticPage();
+  const {
+    createPage,
+    loading: createLoading,
+    error: createError,
+  } = useCreateStaticPage();
+  const {
+    updatePage: updatePageFunc,
+    loading: updateLoading,
+    error: updateError,
+  } = useUpdateStaticPage();
 
   const loading = createLoading || updateLoading;
   const apiError = createError || updateError;
 
-  // Update form data when page prop changes
+  // Reset form when dialog opens or closes
   useEffect(() => {
-    if (page) {
-      setFormData({
-        title: page.title || "",
-        slug: page.slug || "",
-        content: page.content || "",
-      });
-    } else {
-      setFormData({
-        title: "",
-        slug: "",
-        content: "",
-      });
+    if (open) {
+      // Dialog is opening
+      if (page) {
+        // Editing existing page
+        setFormData({
+          title: page.title || '',
+          slug: page.slug || '',
+          content: page.content || '',
+        });
+      } else {
+        // Adding new page - reset all fields
+        setFormData({
+          title: '',
+          slug: '',
+          content: '',
+        });
+        // Generate a new key to force the Editor component to re-render with empty content
+        setEditorKey(Date.now());
+      }
+      setErrors({});
     }
-    setErrors({});
-  }, [page]);
+  }, [open, page]);
 
   // Handle title change and auto-generate slug
   const handleTitleChange = (e) => {
     const title = e.target.value;
     const slug = generateSlug(title);
-    setFormData(prev => ({ ...prev, title, slug }));
+    setFormData((prev) => ({ ...prev, title, slug }));
 
     // Clear title error if it exists
     if (errors.title) {
-      setErrors(prev => ({ ...prev, title: undefined }));
+      setErrors((prev) => ({ ...prev, title: undefined }));
     }
   };
 
   // Handle content change from editor
   const handleContentChange = (content) => {
-    setFormData(prev => ({ ...prev, content }));
+    setFormData((prev) => ({ ...prev, content }));
 
     // Clear content error if it exists
     if (errors.content) {
-      setErrors(prev => ({ ...prev, content: undefined }));
+      setErrors((prev) => ({ ...prev, content: undefined }));
     }
   };
 
@@ -86,11 +106,11 @@ export function StaticPageDialog({ open, onOpenChange, page, onSave }) {
     const newErrors = {};
 
     if (!formData.title.trim()) {
-      newErrors.title = "Title is required";
+      newErrors.title = 'Title is required';
     }
 
     if (!formData.content.trim()) {
-      newErrors.content = "Content is required";
+      newErrors.content = 'Content is required';
     }
 
     setErrors(newErrors);
@@ -125,24 +145,43 @@ export function StaticPageDialog({ open, onOpenChange, page, onSave }) {
       }
 
       if (response?.success) {
-        toast.success(page?.id ? "Page updated successfully" : "Page created successfully");
+        toast.success(
+          page?.id ? 'Page updated successfully' : 'Page created successfully'
+        );
         if (onSave) {
           onSave(payload);
         }
         handleDialogChange(false);
       } else {
-        toast.error(response?.message || "Failed to save page");
+        toast.error(response?.message || 'Failed to save page');
       }
     } catch (error) {
-      console.error("Error saving page:", error);
-      toast.error("An unexpected error occurred");
+      console.error('Error saving page:', error);
+      toast.error('An unexpected error occurred');
     } finally {
       setIsSubmitting(false);
     }
   };
 
+  // Reset form to initial state
+  const resetForm = () => {
+    setFormData({
+      title: '',
+      slug: '',
+      content: '',
+    });
+    setErrors({});
+    setEditorKey(Date.now()); // Force editor to re-render with empty content
+  };
+
   // Handle dialog close
   const handleDialogChange = (open) => {
+    if (!open) {
+      // Dialog is closing, reset the form after a short delay
+      // This ensures the form is reset after the dialog animation completes
+      setTimeout(resetForm, 300);
+    }
+
     if (onOpenChange) {
       onOpenChange(open);
     }
@@ -152,7 +191,7 @@ export function StaticPageDialog({ open, onOpenChange, page, onSave }) {
     <Dialog open={open} onOpenChange={handleDialogChange}>
       <DialogContent className="w-auto max-w-[90vw] min-w-[50vw] flex flex-col">
         <DialogHeader>
-          <DialogTitle>{page ? "Edit Page" : "Add New Page"}</DialogTitle>
+          <DialogTitle>{page ? 'Edit Page' : 'Add New Page'}</DialogTitle>
         </DialogHeader>
         <form onSubmit={handleSubmit} className="flex flex-col gap-4 flex-1">
           {apiError && (
@@ -164,7 +203,10 @@ export function StaticPageDialog({ open, onOpenChange, page, onSave }) {
           )}
 
           <div className="grid w-full items-center gap-2">
-            <Label htmlFor="title" className={errors.title ? "text-destructive" : ""}>
+            <Label
+              htmlFor="title"
+              className={errors.title ? 'text-destructive' : ''}
+            >
               Title
             </Label>
             <Input
@@ -172,7 +214,7 @@ export function StaticPageDialog({ open, onOpenChange, page, onSave }) {
               placeholder="Enter page title"
               value={formData.title}
               onChange={handleTitleChange}
-              className={errors.title ? "border-destructive" : ""}
+              className={errors.title ? 'border-destructive' : ''}
             />
             {errors.title && (
               <p className="text-destructive text-sm">{errors.title}</p>
@@ -190,10 +232,11 @@ export function StaticPageDialog({ open, onOpenChange, page, onSave }) {
           </div>
 
           <div className="flex-1">
-            <Label className={errors.content ? "text-destructive" : ""}>
+            <Label className={errors.content ? 'text-destructive' : ''}>
               Content
             </Label>
             <Editor
+              key={editorKey} // Add key to force re-render when it changes
               initialContent={formData.content}
               onContentChange={handleContentChange}
             />
@@ -211,11 +254,8 @@ export function StaticPageDialog({ open, onOpenChange, page, onSave }) {
             >
               Cancel
             </Button>
-            <Button
-              type="submit"
-              disabled={isSubmitting || loading}
-            >
-              {isSubmitting ? "Saving..." : "Save"}
+            <Button type="submit" disabled={isSubmitting || loading}>
+              {isSubmitting ? 'Saving...' : 'Save'}
             </Button>
           </DialogFooter>
         </form>
diff --git a/src/components/ui/dialog.jsx b/src/components/ui/dialog.jsx
index a780f34..17f4f82 100644
--- a/src/components/ui/dialog.jsx
+++ b/src/components/ui/dialog.jsx
@@ -49,23 +49,23 @@ function DialogContent({
   ...props
 }) {
   return (
-    (<DialogPortal data-slot="dialog-portal">
+    <DialogPortal data-slot="dialog-portal">
       <DialogOverlay />
       <DialogPrimitive.Content
         data-slot="dialog-content"
         className={cn(
-          "bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",
+          'bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg',
           className
         )}
-        {...props}>
+        {...props}
+      >
         {children}
-        <DialogPrimitive.Close
-          className="ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4">
+        <DialogPrimitive.Close className="ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4">
           <XIcon />
           <span className="sr-only">Close</span>
         </DialogPrimitive.Close>
       </DialogPrimitive.Content>
-    </DialogPortal>)
+    </DialogPortal>
   );
 }
 
