import {
  getLocalStorageItem,
  removeLocalStorageItem,
} from '@/lib/helpers/local-storage';
import { useState, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router';

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

function useFetch(endpoint) {
  const navigate = useNavigate();
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const controllerRef = useRef(null);
  const previousEndpointRef = useRef(null);

  const abort = useCallback(() => {
    if (controllerRef.current) {
      controllerRef.current.abort();
      controllerRef.current = null;
    }
  }, []);

  const fn = useCallback(
    async ({
      method = 'GET',
      headers = {},
      body = {},
      query = {},
      customEndpoint = null,
    } = {}) => {
      try {
        setLoading(true);
        setError(null);

        // Cancel previous request if it's the same endpoint
        if (controllerRef.current && previousEndpointRef.current === endpoint) {
          abort();
        }

        controllerRef.current = new AbortController();
        previousEndpointRef.current = endpoint;

        // Add authorization header if token exists
        const accessToken = getLocalStorageItem('icc_tk');
        if (accessToken) {
          headers.Authorization = `Bearer ${accessToken}`;
        }

        // Use customEndpoint if provided, otherwise use the endpoint from the hook
        const finalEndpoint = customEndpoint || endpoint;

        // Prepare URL with query parameters
        const queryString = new URLSearchParams(query).toString();
        const url = `${BASE_URL}${finalEndpoint}${
          queryString ? `?${queryString}` : ''
        }`;

        const config = {
          method,
          headers,
          credentials: 'omit',
          signal: controllerRef.current.signal,
        };

        // Add body for non-GET requests
        if (method !== 'GET' && body) {
          if (body instanceof FormData) {
            config.body = body;
          } else {
            config.body = JSON.stringify(body);
            headers['Content-Type'] = 'application/json';
          }
        }

        const response = await fetch(url, config);
        const data = await response.json();

        if (!response.ok) {
          if (response.status === 400) {
            const message = Array.isArray(data?.message)
              ? data.message[0]
              : data?.message;
            throw { error: true, message, status: response.status };
          }
          if (response.status === 401) {
            removeLocalStorageItem('user');
            removeLocalStorageItem('icc_tk');
            navigate('/');
          }
          throw new Error(data?.message || `Server error: ${response.status}`);
        }

        return data;
      } catch (err) {
        if (err.name === 'AbortError') {
          console.error('Request canceled:', err.message);
          return null;
        }

        const errorMessage =
          err?.message || 'Something went wrong. Please try again.';
        setError(errorMessage);
        throw err; // Allow error to be handled by the caller
      } finally {
        setLoading(false);
        if (controllerRef.current?.signal.aborted) {
          controllerRef.current = null;
        }
      }
    },
    [endpoint, abort]
  );

  return { error, loading, fn, abort };
}

export default useFetch;
