import { BASE_URL } from '@/config/constants';
import useFetch from '../use-fetch';
import { removeLocalStorageItem, setLocalStorageItem } from '@/lib/helpers/local-storage';
import { useContext } from 'react';
import { AuthContext } from '@/context/auth-context';

export function useLogin() {
  const { fn: fetchLogin, error, loading } = useFetch('/auth/login');
  const { setUser } = useContext(AuthContext);

  const login = async (credentials) => {
    try {
      const response = await fetchLogin({
        method: 'POST',
        body: credentials,
      });

      if (response?.success) {
        setLocalStorageItem('icc_tk', response.data.token);
        const userData = {
          name: response.data.userName,
          email: response.data.userEmail,
        };
        setLocalStorageItem('user', JSON.stringify(userData));
        setUser(userData);
        return { success: true, message: response.message };
      }
    } catch (error) {
      console.error('API Error:', error);
      return false;
    }
  };

  return { login, error, loading };
}

export function useForgotPassword() {
  const {
    fn: fetchForgotPassword,
    error,
    loading,
  } = useFetch('/auth/forgot-password');

  const forgotPasswordFunc = async (data) => {
    try {
      const response = await fetchForgotPassword({
        method: 'POST',
        body: data,
      });

      return response;
    } catch (error) {
      console.error('Failed to send forgot password request: ', error);
      return { success: false, error: error.message };
    }
  };

  return { forgotPasswordFunc, error, loading };
}

export function useVerifyOtp() {
  const { fn: verifyOtpFn, error, loading } = useFetch('/auth/verify-otp');

  const verifyOtp = async (data) => {
    try {
      const response = await verifyOtpFn({
        method: 'POST',
        body: data,
      });

      return response;
    } catch (error) {
      console.error('Failed to verify OTP: ', error);
      return { success: false, error: error.message };
    }
  };

  return { verifyOtp, error, loading };
}

export function useResetPassword() {
  const {
    fn: resetPasswordFn,
    error,
    loading,
  } = useFetch('/auth/reset-password');

  const resetPassword = async (data) => {
    try {
      const response = await resetPasswordFn({
        method: 'POST',
        body: data,
      });

      return response;
    } catch (error) {
      console.error('Failed to reset password: ', error);
      return { success: false, error: error.message };
    }
  };

  return { resetPassword, error, loading };
}

export function useLogout() {
  const { fn: fetchLogout, error, loading } = useFetch('/auth/logout');
  const { setUser } = useContext(AuthContext);

  const logout = async (credentials) => {
    try {
      const response = await fetchLogout({
        method: 'GET',
        body: credentials,
      });

      if (response?.success) {
        removeLocalStorageItem('user');
        removeLocalStorageItem('icc_tk');
        setUser(null);
        return { success: true, message: response.message };
      }
    } catch (error) {
      console.error('API Error:', error);
      return false;
    }
  };

  return { logout, error, loading };
}