import useFetch from '@/hooks/use-fetch';

export function useStaticContent() {
  const { fn: fetchStaticContent, error, loading } = useFetch('/static');

  const getStaticPages = async (payload) => {
    try {
      const response = await fetchStaticContent({
        method: 'GET',
        query: payload,
      });
      return response;
    } catch (error) {
      console.error('Failed to fetch static pages: ', error);
      return { success: false, error: error.message };
    }
  };

  return { getStaticPages, error, loading };
}

export function useCreateStaticPage() {
  const { fn: createStaticPage, error, loading } = useFetch('/static');

  const createPage = async (data) => {
    try {
      const response = await createStaticPage({
        method: 'POST',
        body: data,
      });

      return response;
    } catch (error) {
      console.error('Failed to create static page: ', error);
      return { success: false, error: error.message };
    }
  };

  return { createPage, error, loading };
}

export function useUpdateStaticPage() {
  // We'll use a generic endpoint and construct the full path in the function
  const { fn: fetchApi, error, loading } = useFetch('/static');

  const updatePage = async (pageId, data) => {
    if (!pageId) {
      console.error('Page ID is required for update');
      return { success: false, error: 'Page ID is required' };
    }

    try {
      // Use the fetchApi function but override the endpoint with the ID
      const response = await fetchApi({
        method: 'PUT',
        body: data,
        customEndpoint: `/static/${pageId}`, // This will be used in the useFetch hook
      });

      return response;
    } catch (error) {
      console.error('Failed to update static page: ', error);
      return { success: false, error: error.message };
    }
  };

  return { updatePage, error, loading };
}

export function useDeleteStaticPage() {
  // We'll use a generic endpoint and construct the full path in the function
  const { fn: fetchApi, error, loading } = useFetch('/static');

  const deletePage = async (pageId) => {
    if (!pageId) {
      console.error('Page ID is required for deletion');
      return { success: false, error: 'Page ID is required' };
    }

    try {
      // Use the fetchApi function but override the endpoint with the ID
      const response = await fetchApi({
        method: 'DELETE',
        customEndpoint: `/static/${pageId}`, // This will be used in the useFetch hook
      });

      return response;
    } catch (error) {
      console.error('Failed to delete static page: ', error);
      return { success: false, error: error.message };
    }
  };

  return { deletePage, error, loading };
}
