import useFetch from '@/hooks/use-fetch';

export function useCategories() {
  const { fn: fetchCategories, error, loading } = useFetch('/category');

  const getPaginatedCategories = async (payload) => {
    try {
      const response = await fetchCategories({
        method: 'GET',
        query: payload,
      });
      return response;
    } catch (error) {
      console.error('Failed to fetch categories: ', error);
      return { success: false, error: error.message };
    }
  };

  return { getPaginatedCategories, error, loading };
}

export function useUpdateCategory() {
  const { fn: updateCategory, loading, error } = useFetch('/category');

  const updateCategoryFunc = async (data) => {
    try {
      const response = await updateCategory({
        method: 'PUT',
        body: data,
      });

      return response;
    } catch {
      return { success: false, error };
    }
  };

  return { updateCategoryFunc, loading, error };
}

export function useCompanies() {
  const { fn: fetchCompanies, error, loading } = useFetch('/company');

  const getPaginatedCompanies = async (payload) => {
    try {
      const response = await fetchCompanies({
        method: 'GET',
        query: payload,
      });
      return response?.data || [];
    } catch (error) {
      console.error('Failed to fetch companies: ', error);
      return [];
    }
  };

  return { getPaginatedCompanies, error, loading };
}

export function useCompanyDetails(companyId) {
  const {
    fn: fetchCompanyDetail,
    error,
    loading,
  } = useFetch(`/company/${companyId}`);

  const getCompanyDetail = async () => {
    try {
      const response = await fetchCompanyDetail({
        method: 'GET',
      });
      return response || null;
    } catch (error) {
      console.error('Failed to fetch company details: ', error);
      return { success: false, error: error.message };
    }
  };

  return { getCompanyDetail, error, loading };
}

export function useUploadData() {
  const { fn: uploadData, error, loading, abort } = useFetch('/upload');

  const uploadInChunks = async (dataChunks) => {
    try {
      const responses = await Promise.all(
        dataChunks.map((chunk) =>
          uploadData({
            method: 'POST',
            body: { data: chunk },
          })
        )
      );

      return responses.every((response) => response?.success);
    } catch (error) {
      console.error('Failed to upload data:', error);
      return false;
    }
  };

  return { uploadInChunks, error, loading, abort };
}

export function useUploadCategoryIcon() {
  const {
    fn: uploadCategoryIcon,
    error,
    loading,
  } = useFetch('/category/icon-upload');

  const uploadIconFunc = async (formData) => {
    try {
      const response = uploadCategoryIcon({
        method: 'POST',
        body: formData,
      });

      return response || null;
    } catch (error) {
      console.error('Failed to upload category icon:', error);
      return null;
    }
  };

  return { uploadIconFunc, error, loading };
}
