import { useReducer, useMemo, useCallback, useState } from 'react';

/**
 * Custom hook for managing data table UI state including filtering, pagination, and row selection
 * Data fetching and storage is intentionally kept separate
 *
 * @param {Object} options - Configuration options for the hook
 * @param {Object} options.initialQuery - Initial query parameters (optional)
 * @param {string} options.defaultSortBy - Default sort field (optional)
 * @param {string} options.defaultSortOrder - Default sort order (optional)
 * @param {number} options.defaultLimit - Default items per page (optional)
 * @returns {Object} Data table UI state and handlers
 */

const usePagination = (options = {}) => {
  // Configure default initial state with ability to override
  const defaultQuery = {
    search: '',
    page: 1,
    limit: options.defaultLimit || 10,
    sortBy: options.defaultSortBy || 'id',
    sortOrder: options.defaultSortOrder || 'DESC',
  };

  // Merge default query with any provided initialQuery options
  const initialState = {
    ...defaultQuery,
    ...(options.initialQuery || {}),
  };

  // Query state reducer
  const queryReducer = (state, action) => {
    switch (action.type) {
      case 'SET_QUERY':
        return { ...state, ...action.payload };
      case 'RESET_QUERY':
        return initialState;
      default:
        return state;
    }
  };

  // Core state management
  const [query, dispatch] = useReducer(queryReducer, initialState);
  const [totalPages, setTotalPages] = useState(0);
  //   const [selectedRows, setSelectedRows] = useState(options.initialSelectedRows || []);
  //   const [loading, setLoading] = useState(false);

  // Memoized handlers for better performance
  const updateQuery = useCallback((newQuery) => {
    dispatch({ type: 'SET_QUERY', payload: newQuery });
  }, []);

  const resetQuery = useCallback(() => {
    dispatch({ type: 'RESET_QUERY' });
  }, []);

  // Pagination helpers
  const goToPage = useCallback(
    (page) => {
      updateQuery({ page });
    },
    [updateQuery]
  );

  const setPageSize = useCallback(
    (limit) => {
      updateQuery({ limit, page: 1 }); // Reset to first page when changing page size
    },
    [updateQuery]
  );

  // Sorting helpers
  const sort = useCallback(
    (sortBy) => {
      updateQuery({
        sortBy,
        sortOrder:
          query.sortBy === sortBy && query.sortOrder === 'ASC' ? 'DESC' : 'ASC',
        page: 1,
      });
    },
    [query.sortBy, query.sortOrder, updateQuery]
  );

  // Search helper
  const search = useCallback(
    (searchTerm) => {
      updateQuery({ search: searchTerm, page: 1 }); // Reset to first page on new search
    },
    [updateQuery]
  );

  // Selection helpers
  //   const selectRow = useCallback((id) => {
  //     setSelectedRows(prev => {
  //       if (prev.includes(id)) return prev;
  //       return [...prev, id];
  //     });
  //   }, []);

  //   const unselectRow = useCallback((id) => {
  //     setSelectedRows(prev => prev.filter(rowId => rowId !== id));
  //   }, []);

  //   const toggleRowSelection = useCallback((id) => {
  //     setSelectedRows(prev =>
  //       prev.includes(id)
  //         ? prev.filter(rowId => rowId !== id)
  //         : [...prev, id]
  //     );
  //   }, []);

  //   const selectAll = useCallback((ids) => {
  //     if (Array.isArray(ids) && ids.length > 0) {
  //       setSelectedRows(ids);
  //     }
  //   }, []);

  //   const clearSelection = useCallback(() => {
  //     setSelectedRows([]);
  //   }, []);

  // Check if a row is selected
  //   const isSelected = useCallback((id) => {
  //     return selectedRows.includes(id);
  //   }, [selectedRows]);

  // Return memoized value to prevent unnecessary re-renders
  return useMemo(
    () => ({
      // State
      query,
      totalPages,
      // selectedRows,
      // loading,

      // State updaters
      setTotalPages,
      // setSelectedRows,
      // setLoading,

      // Query handlers
      updateQuery,
      resetQuery,

      // Pagination handlers
      goToPage,
      setPageSize,

      // Sorting and filtering
      sort,
      search,

      // Selection handlers
      // selectRow,
      // unselectRow,
      // toggleRowSelection,
      // selectAll,
      // clearSelection,
      // isSelected,
    }),
    [
      query,
      totalPages,
      //   selectedRows,
      //   loading,
      setTotalPages,
      updateQuery,
      resetQuery,
      goToPage,
      setPageSize,
      sort,
      search,
      // selectRow,
      // unselectRow,
      // toggleRowSelection,
      // selectAll,
      // clearSelection,
      // isSelected
    ]
  );
};

export default usePagination;
