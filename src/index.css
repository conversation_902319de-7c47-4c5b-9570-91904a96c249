@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --radius: 0.625rem;
  --background: rgb(255, 255, 255);
  /* --foreground: rgb(20, 20, 20); */
  --foreground: rgb(0, 0, 0);
  /* --card: rgb(255, 255, 255); */
  --card: rgb(242, 245, 249);
  --card-foreground: rgb(20, 20, 20);
  /* --popover: rgb(255, 255, 255); */
  --popover: rgb(242, 245, 249);
  --popover-foreground: rgb(20, 20, 20);
  /* --primary: rgb(33, 33, 33); */
  --primary: rgb(3, 25, 77);
  /* --primary-foreground: rgb(250, 250, 250); */
  --primary-foreground: rgb(255, 255, 255);
  /* --secondary: rgb(247, 247, 247); */
  --secondary: rgb(217, 227, 239);
  --secondary-foreground: rgb(33, 33, 33);
  /* --muted: rgb(247, 247, 247); */
  --muted: rgb(242, 245, 249);
  --muted-foreground: rgb(115, 115, 115);
  /* --accent: rgb(247, 247, 247); */
  --accent: rgb(242, 245, 249);
  --accent-foreground: rgb(33, 33, 33);
  --destructive: rgb(239, 68, 68);
  /* --border: rgb(229, 229, 229); */
  --border: rgb(215, 226, 241);
  /* --input: rgb(229, 229, 229); */
  --input: rgb(215, 226, 241);
  /* --ring: rgb(63, 131, 248); */
  --ring: rgb(217, 227, 239);
  --chart-1: rgb(234, 88, 12);
  --chart-2: rgb(14, 165, 233);
  --chart-3: rgb(99, 102, 241);
  --chart-4: rgb(168, 85, 247);
  --chart-5: rgb(236, 72, 153);
  /* --sidebar: rgb(250, 250, 250); */
  --sidebar: rgb(242, 245, 249);
  --sidebar-foreground: rgb(20, 20, 20);
  /* --sidebar-primary: rgb(33, 33, 33); */
  --sidebar-primary: rgb(3, 25, 77);
  --sidebar-primary-foreground: rgb(250, 250, 250);
  --sidebar-accent: rgb(247, 247, 247);
  --sidebar-accent-foreground: rgb(33, 33, 33);
  /* --sidebar-border: rgb(229, 229, 229); */
  --sidebar-border: rgb(215, 226, 241);
  --sidebar-ring: rgb(63, 131, 248);
}

.dark {
  --background: rgb(20, 20, 20);
  --foreground: rgb(250, 250, 250);
  --card: rgb(33, 33, 33);
  --card-foreground: rgb(250, 250, 250);
  --popover: rgb(33, 33, 33);
  --popover-foreground: rgb(250, 250, 250);
  --primary: rgb(229, 229, 229);
  --primary-foreground: rgb(33, 33, 33);
  --secondary: rgb(38, 38, 38);
  --secondary-foreground: rgb(250, 250, 250);
  --muted: rgb(38, 38, 38);
  --muted-foreground: rgb(163, 163, 163);
  --accent: rgb(38, 38, 38);
  --accent-foreground: rgb(250, 250, 250);
  --destructive: rgb(239, 68, 68);
  --border: rgba(255, 255, 255, 0.1);
  --input: rgba(255, 255, 255, 0.15);
  --ring: rgb(96, 165, 250);
  --chart-1: rgb(59, 130, 246);
  --chart-2: rgb(14, 165, 233);
  --chart-3: rgb(236, 72, 153);
  --chart-4: rgb(168, 85, 247);
  --chart-5: rgb(239, 68, 68);
  --sidebar: rgb(33, 33, 33);
  --sidebar-foreground: rgb(250, 250, 250);
  --sidebar-primary: rgb(59, 130, 246);
  --sidebar-primary-foreground: rgb(250, 250, 250);
  --sidebar-accent: rgb(38, 38, 38);
  --sidebar-accent-foreground: rgb(250, 250, 250);
  --sidebar-border: rgba(255, 255, 255, 0.1);
  --sidebar-ring: rgb(96, 165, 250);
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  :root {
    --sidebar-background: rgb(250, 250, 250);
    --sidebar-foreground: rgb(66, 69, 82);
    --sidebar-primary: rgb(25, 26, 31);
    --sidebar-primary-foreground: rgb(250, 250, 250);
    --sidebar-accent: rgb(244, 244, 245);
    --sidebar-accent-foreground: rgb(25, 26, 31);
    --sidebar-border: rgb(232, 233, 236);
    --sidebar-ring: rgb(99, 159, 255);
  }

  .dark {
    --sidebar-background: rgb(25, 26, 31);
    --sidebar-foreground: rgb(244, 244, 245);
    --sidebar-primary: rgb(86, 145, 254);
    --sidebar-primary-foreground: rgb(255, 255, 255);
    --sidebar-accent: rgb(40, 41, 48);
    --sidebar-accent-foreground: rgb(244, 244, 245);
    --sidebar-border: rgb(40, 41, 48);
    --sidebar-ring: rgb(99, 159, 255);
  }
}
