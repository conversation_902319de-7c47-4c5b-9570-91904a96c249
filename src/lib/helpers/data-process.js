// Constants
const STOP_WORDS = [
  'Customer',
  'Support',
  'Service',
  'Helpline',
  'Help',
  'Contact',
  'Toll',
  'Phone',
  'Call',
  'No.',
];
const PHONE_FIELDS = {
  field_toll_free_numbers2: 'TOLL_FREE',
  field_all_india_numbers: 'ALL_INDIA',
  field_international_no: 'INTERNATIONAL',
};
const PLATFORM_PATTERNS = {
  WEBSITE: /^(https?:\/\/)?([\w\d-]+\.)+[a-z]{2,}(\/.*)?$/i,
  FACEBOOK: /facebook\.com/i,
  INSTAGRAM: /instagram\.com/i,
  LINKEDIN: /linkedin\.com/i,
  YOUTUBE: /youtube\.com|youtu\.be/i,
  X: /(?:twitter\.com|x\.com)/i,
  APP_LINK: /play\.google\.com|apps\.apple\.com/i,
};
const REGEXES = {
  companyName: new RegExp(
    `^([\\w&@.\\-\\s()]+?)(?=\\s+(${STOP_WORDS.join('|')})\\b|\\s*\\d)`,
    'i'
  ),
  phoneSplit: /^([+()0-9.\s-]+)[\s-]*[([]?(.*?)[)\]]?$/,
  url: /https?:\/\/[^\s"')]+/g,
};

const extractUrls = (obj) => {
  const urls = new Set();
  const walk = (o) => {
    if (Array.isArray(o)) o.forEach(walk);
    else if (typeof o === 'object' && o !== null)
      Object.values(o).forEach(walk);
    else if (typeof o === 'string') {
      const found = o.match(REGEXES.url);
      if (found) found.forEach((u) => urls.add(u));
    }
  };
  walk(obj);
  return [...urls];
};

const cleanNumber = (number) => number.replace(/[([{][^)\]}]*$/, '').trim();

export const processJsonDataHelper = (jsonData) => {
  let idCounter = 1;
  const generateId = () => idCounter++;
  const finalOutput = [];

  jsonData.forEach((entry) => {
    const title = entry.title?.trim() || null;
    const parentCompany = entry.field_company?.[0]?.value?.trim() || null;

    const match = title.match(REGEXES.companyName);
    const companyName = match
      ? match[1].trim()
      : title.replace(/\d[\d\s\-().]+$/, '').trim();
    const companyId = generateId();

    // Company info
    const company = {
      id: companyId,
      company_name: companyName,
      parent_company: parentCompany,
      company_email: entry.field_email?.[0]?.value || null,
      company_logo_url: entry.field_company_logo?.[0]?.filepath
        ? '/' + entry.field_company_logo?.[0]?.filepath
        : null,
      company_country: entry.field_country?.[0]?.value || null,
      company_address: entry.field_address?.[0]?.value || null,
      company_website: entry.field_website?.[0]?.url || null,
    };

    // Contact numbers
    const contact = [];
    for (const [field, type] of Object.entries(PHONE_FIELDS)) {
      if (entry[field]) {
        entry[field].forEach((item) => {
          if (item && item.value) {
            const trimmed = item.value.trim();
            const match = trimmed.match(REGEXES.phoneSplit);
            if (match) {
              let number = cleanNumber(match[1].trim().replace(/\s+/g, ' '));
              const description = match[2]?.trim() || null;
              contact.push({
                company_id: companyId,
                contact_type: type,
                number,
                contact_description: description,
                is_whatsapp: /whatsapp/i.test(number + description),
              });
            }
          }
        });
      }
    }

    // Categories
    const taxonomy = entry.taxonomy || {};
    const category = Object.values(taxonomy)
      .filter((t) => t.vid === '5')
      .map((t) => ({
        company_id: companyId,
        category_name: t.name,
      }));

    if (category.length === 0) {
      category.push({
        company_id: companyId,
        category_name: 'Other',
      });
    }

    // Taxonomy terms
    const taxonomyList = Object.values(taxonomy).map((t) => ({
      name: t.name,
      description: '',
      weight: t.weight || null,
      weight_unused: t.weight_unused || null,
    }));

    // Company URLs
    const allUrls = extractUrls(entry);
    const seen = new Set();
    const url = [];

    for (const u of allUrls) {
      for (const [type, regex] of Object.entries(PLATFORM_PATTERNS)) {
        if (!seen.has(type) && regex.test(u)) {
          url.push({
            company_id: companyId,
            url: u,
            url_type: type,
          });
          seen.add(type);
        }
      }
    }

    const rawWebsite = entry.field_website?.[0]?.url;
    if (rawWebsite && !seen.has('WEBSITE')) {
      url.push({
        company_id: companyId,
        url: rawWebsite,
        url_type: 'WEBSITE',
      });
    }

    // Final structured object
    finalOutput.push({
      id: companyId,
      company,
      contact,
      category,
      taxonomy: taxonomyList,
      url,
    });
  });

  return finalOutput;
};

export function splitArrayIntoChunks(array, chunkSize) {
  const chunks = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
}