import { memo, useCallback, useMemo } from 'react';
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';

export const DataTablePagination = memo(
  ({
    table,
    pageSizeOptions = [10, 20, 30, 40, 50],
    className,
    pagination,
    ...props
  }) => {
    const { query, totalPages, setPageSize, goToPage } = pagination;

    const handlePageSizeChange = useCallback(
      (value) => {
        table.setPageSize(Number(value));
        setPageSize(Number(value));
      },
      [table, setPageSize]
    );

    const pageSizeItems = useMemo(
      () =>
        pageSizeOptions.map((pageSize) => (
          <SelectItem
            className="cursor-pointer"
            key={pageSize}
            value={`${pageSize}`}
          >
            {pageSize}
          </SelectItem>
        )),
      [pageSizeOptions]
    );

    return (
      <div
        className={cn(
          'flex w-full flex-col-reverse items-center justify-between gap-4 overflow-auto p-1 sm:flex-row sm:gap-8',
          className
        )}
        {...props}
      >
        {/* <div className="flex-1 whitespace-nowrap text-muted-foreground text-sm">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div> */}
        <div className="flex flex-col-reverse items-center gap-4 sm:flex-row sm:gap-6 lg:gap-8">
          <div className="flex items-center space-x-2">
            <p className="whitespace-nowrap font-medium text-sm">
              Rows per page
            </p>
            <Select
              value={`${query.limit}`}
              onValueChange={handlePageSizeChange}
            >
              <SelectTrigger className="h-8 w-[4.5rem]">
                <SelectValue
                  placeholder={table.getState().pagination.pageSize}
                />
              </SelectTrigger>
              <SelectContent side="top">{pageSizeItems}</SelectContent>
            </Select>
          </div>
          <div className="flex items-center justify-center font-medium text-sm">
            Page {query.page} of {totalPages || 0}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              aria-label="Go to first page"
              variant="outline"
              size="icon"
              className="hidden size-8 lg:flex"
              onClick={() => goToPage(1)}
              disabled={query.page === 1}
            >
              <ChevronsLeft />
            </Button>
            <Button
              aria-label="Go to previous page"
              variant="outline"
              size="icon"
              className="size-8"
              onClick={() => goToPage(query.page - 1)}
              disabled={query.page === 1}
            >
              <ChevronLeft />
            </Button>
            <Button
              aria-label="Go to next page"
              variant="outline"
              size="icon"
              className="size-8"
              onClick={() => goToPage(query.page + 1)}
              disabled={query.page === totalPages}
            >
              <ChevronRight />
            </Button>
            <Button
              aria-label="Go to last page"
              variant="outline"
              size="icon"
              className="hidden size-8 lg:flex"
              onClick={() => goToPage(totalPages)}
              disabled={query.page === totalPages}
            >
              <ChevronsRight />
            </Button>
          </div>
        </div>
      </div>
    );
  }
);

DataTablePagination.displayName = 'DataTablePagination';
