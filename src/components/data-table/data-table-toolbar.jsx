import { useCallback, useEffect, useState, memo } from 'react';
import { Input } from '@/components/ui/input';
import useDebounce from '@/hooks/use-debounce';
import { X } from 'lucide-react';
import { Button } from '../ui/button';

const DataTableToolbar = memo(({ pagination }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebounce(searchTerm, 500);
  const search = pagination?.search;

  const handleSearch = useCallback(
    (term) => {
      if (term.length >= 1 && search) {
        search(term);
      }
    },
    [search]
  );

  useEffect(() => {
    if (search) {
      handleSearch(debouncedSearchTerm);
    }
  }, [debouncedSearchTerm, handleSearch, search]);

  const handleInputChange = useCallback((e) => {
    setSearchTerm(e.target.value);
  }, []);

  function handleClearSearch() {
    if (debouncedSearchTerm) {
      setSearchTerm('');
      search('');
    }
  }

  return (
    <div className="relative flex m-2 min-w-[10rem] max-w-[35rem]">
      <Input
        className="w-full md:w-[35rem] shadow-sm flex-grow"
        placeholder="Search by any field"
        value={searchTerm}
        onChange={handleInputChange}
      />
      <Button
        variant={'ghost'}
        size={'icon'}
        className={'bg-accent absolute right-0.5'}
        onClick={handleClearSearch}
      >
        <X />
      </Button>
    </div>
  );
});

DataTableToolbar.displayName = 'DataTableToolbar';

export default DataTableToolbar;
