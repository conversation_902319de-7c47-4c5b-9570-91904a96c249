import { useState, useEffect } from 'react';
import { GripVertical } from 'lucide-react';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import {
  flexRender,
  useReactTable,
  getCoreRowModel,
} from '@tanstack/react-table';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { DataTablePagination } from './data-table-pagination';
import DataTableToolbar from './data-table-toolbar';

const DraggableRow = ({ row }) => {
  const [, dropRef] = useDrop({
    accept: 'row',
    // drop: (draggedRow) => reorderRow(draggedRow.index, row.index),
    drop: (draggedRow) => console.log('Dropped', draggedRow, row),
  });

  const [{ isDragging }, dragRef, previewRef] = useDrag({
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
    item: () => row,
    type: 'row',
  });

  return (
    <TableRow
      ref={previewRef}
      className={isDragging ? 'opacity-50' : 'opacity-100'}
    >
      <TableCell ref={dropRef}>
        <Button variant="ghost" ref={dragRef} className="cursor-move p-0">
          <GripVertical />
        </Button>
      </TableCell>
      {row.getVisibleCells().map((cell) => (
        <TableCell key={cell.id}>
          {flexRender(cell.column.columnDef.cell, cell.getContext())}
        </TableCell>
      ))}
    </TableRow>
  );
};

const RegularRow = ({ row }) => {
  return (
    <TableRow>
      {row.getVisibleCells().map((cell) => (
        <TableCell key={cell.id}>
          {flexRender(cell.column.columnDef.cell, cell.getContext())}
        </TableCell>
      ))}
    </TableRow>
  );
};

export default function DataTable({
  columns,
  incomingData,
  title,
  description,
  isLoading,
  isDraggable = false,
  pagination,
}) {
  // const { query, updateQuery, selectedRows, setSelectedRows } =
  //   useContext(ProductContext);
  const [data, setData] = useState(incomingData || []);

  // const handleSort = (columnId) => {
  //   let newSortOrder = "asc";
  //   if (query.sortBy === columnId && query.sortOrder === "asc") {
  //     newSortOrder = "desc";
  //   }
  //   updateQuery({ sortBy: columnId, sortOrder: newSortOrder });
  // };

  // const toggleRowSelection = (productId) => {
  //   setSelectedRows((prevSelectedRows) => {
  //     if (prevSelectedRows.includes(productId)) {
  //       return prevSelectedRows.filter((id) => id !== productId); // unselect
  //     } else {
  //       return [...prevSelectedRows, productId]; // select
  //     }
  //   });
  // };

  // const handleSelectAll = (table, isSelected) => {
  //   const pageRows = table.getRowModel().rows;

  //   setSelectedRows((prevSelectedRows) => {
  //     let updatedSelectedRows = [...prevSelectedRows];

  //     pageRows.forEach((row) => {
  //       const rowId = row.original.productId;

  //       if (isSelected) {
  //         // Add productId to selected rows if not already present
  //         if (!updatedSelectedRows.includes(rowId)) {
  //           updatedSelectedRows.push(rowId);
  //         }
  //       } else {
  //         // Remove productId from selected rows
  //         updatedSelectedRows = updatedSelectedRows.filter(
  //           (id) => id !== rowId
  //         );
  //       }
  //     });

  //     return updatedSelectedRows;
  //   });
  // };

  // const tableColumns = columns({
  //   handleSort,
  //   selectedRows,
  //   toggleRowSelection,
  //   handleSelectAll,
  //   query,
  // });
  const tableColumns = typeof columns === 'function' ? columns() : columns;

  const table = useReactTable({
    columns: tableColumns,
    data,
    getCoreRowModel: getCoreRowModel(),
  });

  // const reorderRow = async (draggedRowIndex, targetRowIndex) => {
  //   const draggedRow = data[draggedRowIndex];
  //   const targetRow = data[targetRowIndex];

  //   data.splice(targetRowIndex, 0, data.splice(draggedRowIndex, 1)[0]);
  //   setData([...data]);

  //   const updatedRows = [
  //     {
  //       productId: draggedRow.productId,
  //       newOrder: targetRow.order, // The order of the target row
  //     },
  //     {
  //       productId: targetRow.productId,
  //       newOrder: draggedRow.order, // The order of the dragged row
  //     },
  //   ];

  //   const response = await productHandler.reorderProducts({
  //     products: updatedRows,
  //   });

  //   if (response?.success === false) {
  //     data.splice(draggedRowIndex, 0, data.splice(targetRowIndex, 1)[0]);
  //     setData([...data]);
  //   }
  // };

  useEffect(() => {
    if (Array.isArray(incomingData)) {
      setData(incomingData);
    }
  }, [incomingData]);

  // useEffect(() => {
  //   table.toggleAllRowsSelected(false);
  // }, [query, table]);

  return (
    <>
      <div className="flex flex-wrap justify-between">
        <div className="flex flex-col justify-center m-2">
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </div>
        <DataTableToolbar pagination={pagination} table={table} />
      </div>
      <Table>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {isDraggable && <TableHead></TableHead>}
              {headerGroup.headers.map((header) => {
                return (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                );
              })}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {isLoading ? (
            <TableRow>
              <TableCell
                colSpan={
                  isDraggable ? tableColumns?.length + 1 : tableColumns?.length
                }
                className="h-24 font-bold text-center"
              >
                Loading...
              </TableCell>
            </TableRow>
          ) : table.getRowModel().rows?.length ? (
            isDraggable ? (
              <DndProvider backend={HTML5Backend}>
                {table.getRowModel().rows.map((row) => (
                  <DraggableRow
                    key={row.id}
                    row={row}
                    // reorderRow={reorderRow}
                  />
                ))}
              </DndProvider>
            ) : (
              table
                .getRowModel()
                .rows.map((row) => <RegularRow key={row.id} row={row} />)
            )
          ) : (
            <TableRow>
              <TableCell
                colSpan={
                  isDraggable ? tableColumns?.length + 1 : tableColumns?.length
                }
                className="h-24 text-center"
              >
                No results.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      <div className="flex items-center justify-end space-x-2 w-full">
        {pagination && (
          <DataTablePagination
            className={'justify-end'}
            table={table}
            pagination={pagination}
          />
        )}
      </div>
    </>
  );
}
