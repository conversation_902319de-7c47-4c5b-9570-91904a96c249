import React, { useEffect } from 'react';

import { useState, useRef } from 'react';
import { AlertCircle, CheckCircle2, FileJson, Upload, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { BeatLoader } from 'react-spinners';
import {
  processJsonDataHelper,
  splitArrayIntoChunks,
} from '@/lib/helpers/data-process';
import { useUploadData } from '@/hooks/api/data.api';

// Maximum file size in bytes (100MB)
const MAX_FILE_SIZE = 100 * 1024 * 1024;

export function JsonUploadDialog() {
  const [open, setOpen] = useState(false);
  const [file, setFile] = useState(null);
  const [jsonData, setJsonData] = useState(null);
  const [error, setError] = useState(null);
  const [isDragging, setIsDragging] = useState(false);
  const [processingState, setProcessingState] = useState('idle');
  const [processingProgress, setProcessingProgress] = useState(0);
  const fileInputRef = useRef(null);

  const { uploadInChunks } = useUploadData();

  useEffect(() => {
    if (!open) {
      // Don't reset file and jsonData when closing
      setProcessingState('idle');
      setProcessingProgress(0);
    }
  }, [open]);

  useEffect(() => {
    if (processingState === 'processing') {
      setProcessingProgress(25);
    } else if (processingState === 'retrieving') {
      setProcessingProgress(50);
    } else if (processingState === 'uploading') {
      setProcessingProgress(75);
    } else if (processingState === 'completed') {
      setProcessingProgress(100);
    }
  }, [processingState]);

  const handleFileChange = (selectedFile) => {
    setError(null);

    if (!selectedFile) {
      setFile(null);
      setJsonData(null);
      return;
    }

    // Check file type
    if (!selectedFile.name.endsWith('.json')) {
      setError('Only JSON files are allowed');
      return;
    }

    // Check file size
    if (selectedFile.size > MAX_FILE_SIZE) {
      setError(
        `File size exceeds the limit of ${MAX_FILE_SIZE / (1024 * 1024)}MB`
      );
      setFile(null);
      return;
    }

    setFile(selectedFile);

    // Read file content
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result;
        const parsed = JSON.parse(content);
        setJsonData(parsed);
      } catch (error) {
        setError('Invalid JSON format');
        setFile(null);
      }
    };
    reader.readAsText(selectedFile);
    reader.onerror = () => {
      setError('Failed to read file');
      setFile(null);
    };
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFileChange(e.dataTransfer.files[0]);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const clearFile = () => {
    setFile(null);
    setJsonData(null);
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const processJsonData = async () => {
    if (!jsonData) return;

    try {
      setProcessingState('processing');

      // Simulate processing delay
      const startTime = performance.now();
      const processedData = processJsonDataHelper(jsonData);
      const endTime = performance.now();
      console.log(`processJsonDataHelper took ${endTime - startTime}ms`);
      // const processedData = processJsonDataHelper(jsonData);

      setProcessingState('retrieving');
      // Simulate data retrieval delay
      await new Promise((resolve) => setTimeout(resolve, 1500));

      setProcessingState('uploading');

      // Simulate upload delay
      // await new Promise((resolve) => setTimeout(resolve, 1500));
      const chunkSize = 100;
      const dataChunks = splitArrayIntoChunks(processedData, chunkSize);
      await uploadInChunks(dataChunks);

      // Process the JSON data here
      // console.log('Processed JSON data:', processedData);

      setProcessingState('completed');

      // Close dialog after showing completion for a moment
      setTimeout(() => {
        setOpen(false);
        // Reset processing state after dialog closes
        setTimeout(() => {
          setProcessingState('idle');
        }, 300);
      }, 1000);
    } catch (err) {
      console.error('Error processing JSON:', err);
      setProcessingState('error');
      setError('Failed to process JSON data');
    }
  };

  const getProcessingMessage = () => {
    switch (processingState) {
      case 'processing':
        return 'Processing the JSON data...';
      case 'retrieving':
        return 'Retrieving the important data...';
      case 'uploading':
        return 'Uploading the data...';
      case 'completed':
        return 'Upload completed successfully!';
      case 'error':
        return 'Error processing data';
      default:
        return '';
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="gap-2">
          <FileJson className="h-4 w-4" />
          Upload JSON
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Upload JSON File</DialogTitle>
          <DialogDescription>
            Upload a JSON file to view and process its data. Maximum file size
            is 100MB.
          </DialogDescription>
        </DialogHeader>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {processingState !== 'idle' && processingState !== 'error' ? (
          <div className="py-8 space-y-6">
            <div className="flex flex-col items-center justify-center text-center space-y-4">
              {processingState === 'completed' ? (
                <CheckCircle2 className="h-10 w-10 text-green-500 animate-in fade-in" />
              ) : (
                <BeatLoader className="text-primary" />
              )}
              <div>
                <p className="text-lg font-medium">{getProcessingMessage()}</p>
                <p className="text-sm text-muted-foreground mt-1">
                  {processingState !== 'completed' &&
                    "Please don't close this window"}
                </p>
              </div>
            </div>
            <Progress value={processingProgress} className="h-2" />
          </div>
        ) : (
          <>
            <div
              className={`mt-4 border-2 border-dashed rounded-lg p-6 text-center ${
                isDragging
                  ? 'border-primary bg-primary/5'
                  : 'border-muted-foreground/20'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <input
                type="file"
                ref={fileInputRef}
                accept=".json"
                className="hidden"
                onChange={(e) => handleFileChange(e.target.files?.[0] || null)}
              />

              {!file ? (
                <div className="space-y-4">
                  <div className="flex justify-center">
                    <Upload className="h-10 w-10 text-muted-foreground" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">
                      Drag and drop your JSON file here or click to browse
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Only .json files up to 100MB are supported
                    </p>
                  </div>
                  <Button variant="secondary" onClick={triggerFileInput}>
                    Select File
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <FileJson className="h-8 w-8 text-primary" />
                      <div className="text-left">
                        <p className="text-sm font-medium truncate max-w-[300px]">
                          {file.name}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {(file.size / 1024).toFixed(2)} KB
                        </p>
                      </div>
                    </div>
                    <Button variant="ghost" size="icon" onClick={clearFile}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <Progress
                    value={(file.size / MAX_FILE_SIZE) * 100}
                    className="h-2"
                  />
                </div>
              )}
            </div>
          </>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button disabled={!file || !!error} onClick={processJsonData}>
            Upload
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
