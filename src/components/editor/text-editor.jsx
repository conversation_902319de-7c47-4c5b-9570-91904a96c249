import React, { useEffect, useState } from 'react';
import 'jodit';
import 'jodit/es2021/jodit.min.css';
import './text-editor.css';
import JoditEditor from 'jodit-react';

const buttons = [
  'undo',
  'redo',
  '|',
  'bold',
  'strikethrough',
  'underline',
  'italic',
  '|',
  'superscript',
  'subscript',
  '|',
  'align',
  '|',
  'ul',
  'ol',
  'outdent',
  'indent',
  '|',
  'font',
  'fontsize',
  'brush',
  'paragraph',
  '|',
  'image',
  'link',
  'table',
  '|',
  'hr',
  'eraser',
  'copyformat',
  '|',
];

const editorConfig = {
  readonly: false,
  // popupRoot: document.body,
  iframe: true,
  toolbar: true,
  spellcheck: true,
  language: 'en',
  toolbarButtonSize: 'medium',
  toolbarAdaptive: true,
  showCharsCounter: false,
  showWordsCounter: false,
  showXPathInStatusbar: false,
  askBeforePasteHTML: true,
  askBeforePasteFromWord: true,
  defaultActionOnPaste: 'insert_clear_html',
  buttons: buttons,
  uploader: {
    insertImageAsBase64URI: true,
  },
  height: 500,
  allowResizeY: false,
};

function Editor({ initialContent = '', onContentChange }) {
  // Initialize data state with initialContent only once
  const [data, setData] = useState(initialContent);

  // Update data when initialContent changes (e.g., when editing a different page)
  useEffect(() => {
    setData(initialContent);
  }, [initialContent]);

  // Handle content changes
  const handleChange = (newContent) => {
    setData(newContent);
    if (onContentChange) {
      onContentChange(newContent);
    }
  };

  return (
    <div id="jodit-editor">
      <JoditEditor
        style={{
          flex: 1,
          width: '100%',
          margin: '0 auto',
        }}
        value={data}
        config={editorConfig}
        onChange={handleChange}
      />
    </div>
  );
}

export default Editor;
