import * as React from 'react';
import { useLocation } from 'react-router';
import { Building, Grid2X2, Notebook } from 'lucide-react';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { NavHeader } from '@/components/nav-header';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from '@/components/ui/sidebar';

export function AppSidebar({ ...props }) {
  const location = useLocation();
  const pathParts = location.pathname.split('/');
  const activePath = pathParts[2];

  const data = {
    user: {
      name: 'admin',
      email: '<EMAIL>',
      // avatar: '/avatars/shadcn.jpg',
    },
    navMain: [
      {
        title: 'Categories',
        url: '/dashboard/categories',
        icon: Grid2X2,
        isActive: activePath === 'categories',
      },
      {
        title: 'Companies',
        url: '/dashboard/companies',
        icon: Building,
        isActive: activePath === 'companies',
      },
      {
        title: 'Pages',
        url: '/dashboard/static',
        icon: Notebook,
        isActive: activePath === 'static',
      },
    ],
  };

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <NavHeader />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
