import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '../ui/button';
import { columns } from './static-pages-columns';
import DataTable from '../data-table/data-table';
import { StaticPageDialog } from './static-page-dialog';
import { DeleteConfirmationDialog } from './delete-confirmation-dialog';
import { PencilIcon, TrashIcon, PlusIcon, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { useStaticContent, useDeleteStaticPage } from '@/hooks/api/static-content.api';

const StaticContentPage = () => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedPage, setSelectedPage] = useState(null);
  const [pageToDelete, setPageToDelete] = useState(null);
  const [pages, setPages] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  const { getStaticPages } = useStaticContent();
  const { deletePage, loading: deleteLoading } = useDeleteStaticPage();

  // Fetch static pages on component mount
  useEffect(() => {
    fetchPages();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchPages = async () => {
    setIsLoading(true);
    try {
      const response = await getStaticPages();
      if (response?.success) {
        setPages(response.data || []);
      } else if (response?.error) {
        toast.error( response?.error || "Failed to load static pages");
      }
    } catch (error) {
      console.error("Error fetching static pages:", error);
      toast.error("An error occurred while loading pages");
    } finally {
      setIsLoading(false);
    }
  };

  const handleAdd = () => {
    setSelectedPage(null);
    setDialogOpen(true);
  };

  const handleEdit = (page) => {
    setSelectedPage(page);
    setDialogOpen(true);
  };

  const handleDelete = (page) => {
    setPageToDelete(page);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async (page) => {
    try {
      const response = await deletePage(page.id);
      if (response?.success) {
        setPages(prev => prev.filter(p => p.id !== page.id));
        toast.success("Page deleted successfully");
        setDeleteDialogOpen(false);
      } else {
        toast.error(response?.message || "Failed to delete page");
      }
    } catch (error) {
      console.error("Error deleting page:", error);
      toast.error("An error occurred while deleting the page");
    }
  };

  const handleSave = (formData) => {
    // This will be called after successful API call in the dialog
    if (selectedPage) {
      // Update existing page in the local state
      setPages(prev => prev.map(p =>
        p.id === selectedPage.id ? { ...p, ...formData } : p
      ));
    } else {
      // Refresh the page list to get the new page with its ID from the server
      fetchPages();
    }
  };

  return (
    <>
      <DataTable
          columns={columns.map(col => col.id === 'actions' ? {
            ...col,
            cell: ({ row }) => {
              const page = row.original;
              return (
                <div className="flex gap-2 justify-end">
                  <Button variant="ghost" size="icon" onClick={() => handleEdit(page)}>
                    <PencilIcon className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDelete(page)}
                    disabled={deleteLoading}
                  >
                    <TrashIcon className="h-4 w-4" />
                  </Button>
                </div>
              );
            }
          } : col)}
          incomingData={pages}
          title={'Static Pages'}
          description={'Manage your static pages'}
          isLoading={isLoading}
        />

      <StaticPageDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        page={selectedPage}
        onSave={handleSave}
      />

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        page={pageToDelete}
        onConfirm={confirmDelete}
        isDeleting={deleteLoading}
      />
      <Button
        onClick={handleAdd}
        className="fixed bottom-6 right-6 rounded-full shadow-lg"
        size="lg"
        disabled={isLoading}
      >
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Loading...
          </>
        ) : (
          <>
            <PlusIcon className="mr-2 h-4 w-4" />
            Add New Page
          </>
        )}
      </Button>
    </>
  );
};

export default StaticContentPage;
