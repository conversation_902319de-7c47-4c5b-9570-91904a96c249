import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>T<PERSON>le,
  DialogFooter,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import Editor from '../editor/text-editor';
import {
  useCreateStaticPage,
  useUpdateStaticPage,
} from '@/hooks/api/static-content.api';

// Function to generate slug from title
const generateSlug = (title) => {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .trim(); // Trim leading/trailing spaces
};

export function StaticPageDialog({ open, onOpenChange, page, onSave }) {
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    content: '',
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  // Add a key state to force re-render of the Editor component
  const [editorKey, setEditorKey] = useState(Date.now());

  // Use a single hook for both create and update operations
  const {
    createPage,
    loading: createLoading,
    error: createError,
  } = useCreateStaticPage();
  const {
    updatePage: updatePageFunc,
    loading: updateLoading,
    error: updateError,
  } = useUpdateStaticPage();

  const loading = createLoading || updateLoading;
  const apiError = createError || updateError;

  // Reset form when dialog opens or closes
  useEffect(() => {
    if (open) {
      // Dialog is opening
      if (page) {
        // Editing existing page
        setFormData({
          title: page.title || '',
          slug: page.slug || '',
          content: page.content || '',
        });
      } else {
        // Adding new page - reset all fields
        setFormData({
          title: '',
          slug: '',
          content: '',
        });
        // Generate a new key to force the Editor component to re-render with empty content
        setEditorKey(Date.now());
      }
      setErrors({});
    }
  }, [open, page]);

  // Handle title change and auto-generate slug
  const handleTitleChange = (e) => {
    const title = e.target.value;
    const slug = generateSlug(title);
    setFormData((prev) => ({ ...prev, title, slug }));

    // Clear title error if it exists
    if (errors.title) {
      setErrors((prev) => ({ ...prev, title: undefined }));
    }
  };

  // Handle content change from editor
  const handleContentChange = (content) => {
    setFormData((prev) => ({ ...prev, content }));

    // Clear content error if it exists
    if (errors.content) {
      setErrors((prev) => ({ ...prev, content: undefined }));
    }
  };

  // Validate form data
  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.content.trim()) {
      newErrors.content = 'Content is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const payload = {
        title: formData.title,
        slug: formData.slug,
        content: formData.content,
      };

      let response;

      if (page?.id) {
        // Update existing page
        response = await updatePageFunc(page.id, payload);
      } else {
        // Create new page
        response = await createPage(payload);
      }

      if (response?.success) {
        toast.success(
          page?.id ? 'Page updated successfully' : 'Page created successfully'
        );
        if (onSave) {
          onSave(payload);
        }
        handleDialogChange(false);
      } else {
        toast.error(response?.message || 'Failed to save page');
      }
    } catch (error) {
      console.error('Error saving page:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Reset form to initial state
  const resetForm = () => {
    setFormData({
      title: '',
      slug: '',
      content: '',
    });
    setErrors({});
    setEditorKey(Date.now()); // Force editor to re-render with empty content
  };

  // Handle dialog close
  const handleDialogChange = (open) => {
    if (!open) {
      // Dialog is closing, reset the form after a short delay
      // This ensures the form is reset after the dialog animation completes
      setTimeout(resetForm, 300);
    }

    if (onOpenChange) {
      onOpenChange(open);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleDialogChange}>
      <DialogContent className="w-auto max-w-[90vw] min-w-[50vw] flex flex-col">
        <DialogHeader>
          <DialogTitle>{page ? 'Edit Page' : 'Add New Page'}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="flex flex-col gap-4 flex-1">
          {apiError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{apiError}</AlertDescription>
            </Alert>
          )}

          <div className="grid w-full items-center gap-2">
            <Label
              htmlFor="title"
              className={errors.title ? 'text-destructive' : ''}
            >
              Title
            </Label>
            <Input
              id="title"
              placeholder="Enter page title"
              value={formData.title}
              onChange={handleTitleChange}
              className={errors.title ? 'border-destructive' : ''}
            />
            {errors.title && (
              <p className="text-destructive text-sm">{errors.title}</p>
            )}
          </div>

          <div className="grid w-full items-center gap-2">
            <Label htmlFor="slug">Slug (auto-generated)</Label>
            <Input
              id="slug"
              value={formData.slug}
              disabled
              className="bg-muted"
            />
          </div>

          <div className="flex-1">
            <Label className={errors.content ? 'text-destructive' : ''}>
              Content
            </Label>
            <Editor
              key={editorKey} // Add key to force re-render when it changes
              initialContent={formData.content}
              onContentChange={handleContentChange}
            />
            {errors.content && (
              <p className="text-destructive text-sm mt-1">{errors.content}</p>
            )}
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => handleDialogChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting || loading}>
              {isSubmitting ? 'Saving...' : 'Save'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
