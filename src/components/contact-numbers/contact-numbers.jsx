import { useState } from 'react';
import { DashboardShell } from '@/components/dashboard-shell';
import DataTable from '@/components/data-table/data-table';
import { columns } from './contact-numbers-columns';

export default function ContactNumbersPage() {
  const [data] = useState([
    {
      name: 'Customer Support',
      number: '+1234567890',
      company: 'TechCorp',
      category: 'Support',
      date: '2023-01-15',
      status: 'Active',
    },
    {
      name: 'Sales Hotline',
      number: '+1987654321',
      company: 'Global Systems',
      category: 'Sales',
      date: '2023-02-20',
      status: 'Active',
    },
    {
      name: 'Technical Support',
      number: '+1122334455',
      company: 'DataFlow Inc',
      category: 'Technical',
      date: '2023-03-10',
      status: 'Inactive',
    },
  ]);

  return <DataTable columns={columns} incomingData={data} isLoading={false} />;
}
