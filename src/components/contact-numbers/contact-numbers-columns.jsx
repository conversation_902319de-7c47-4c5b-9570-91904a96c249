export const columns = () => [
  {
    id: 'name',
    header: 'Name',
    cell: ({ row }) => row.original.name,
  },
  {
    id: 'number',
    header: 'Number',
    cell: ({ row }) => row.original.number,
  },
  {
    id: 'company',
    header: 'Company',
    cell: ({ row }) => row.original.company,
  },
  {
    id: 'category',
    header: 'Category',
    cell: ({ row }) => row.original.category,
  },
  {
    id: 'date',
    header: 'Created Date',
    cell: ({ row }) => row.original.date,
  },
  {
    id: 'status',
    header: 'Status',
    cell: ({ row }) => (
      <span
        className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
          row.original.status === 'Active'
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
        }`}
      >
        {row.original.status}
      </span>
    ),
  },
];
