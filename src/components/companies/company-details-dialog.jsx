import React, { useEffect, useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Globe, Mail, MapPin, Building2, Calendar, Eye } from 'lucide-react';
// import { format } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { useCompanyDetails } from '@/hooks/api/data.api';
import { toast } from 'sonner';

function ContactNumberCard({ title, numbers }) {
  if (!numbers || numbers.length === 0) return null;

  return (
    <Card className={'gap-2'}>
      <CardHeader>
        <CardTitle className="text-base">{title}</CardTitle>
      </CardHeader>
      <CardContent className="grid gap-2">
        {numbers.map((number) => (
          <div
            key={number.numberId}
            className="flex items-start justify-between gap-2 text-sm"
          >
            <div>
              <p className="font-medium">{number.number}</p>
              <p className="text-muted-foreground text-xs">
                {number.description}
              </p>
            </div>
            <div className="flex gap-2">
              {number.isWhatsapp && (
                <Badge variant="outline" className="text-green-600">
                  WhatsApp
                </Badge>
              )}
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}

export function CompanyDetailsDialog({ company }) {
  const [companyDetail, setCompanyDetail] = useState(null);
  const [open, setOpen] = useState(false)

  const { getCompanyDetail } = useCompanyDetails(company.companyId);

  async function fetchCompany() {
    const res = await getCompanyDetail();

    if (res && !res.success) {
      toast.error(res.error);
      return;
    }
    setCompanyDetail(res.data);
  }

  useEffect(() => {
    if (open) {
      fetchCompany();
    }
  }, [open])
  
  if (!company) return null;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger className="h-5 w-5 inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-all cursor-pointer" asChild>
        <Eye className="h-4 w-4 text-primary" />
      </DialogTrigger>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center gap-4">
            {companyDetail?.companyLogoUrl && (
              <img
                src={companyDetail?.companyLogoUrl}
                alt={companyDetail?.companyName}
                className="h-auto w-24"
              />
            )}
            <div>
              <DialogTitle>{companyDetail?.companyName}</DialogTitle>
              {companyDetail?.parentCompany && (
                <DialogDescription>
                  <span className="flex items-center gap-1">
                    <Building2 className="h-3 w-3" />
                    {companyDetail?.parentCompany}
                  </span>
                </DialogDescription>
              )}
            </div>
          </div>
        </DialogHeader>

        <div className="grid gap-6">
          {/* Company Info */}
          <div className="grid gap-2 text-sm">
            {companyDetail?.companyEmail && (
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span>{companyDetail?.companyEmail}</span>
              </div>
            )}
            {companyDetail?.companyWebsite && (
              <div className="flex items-center gap-2">
                <Globe className="h-4 w-4 text-muted-foreground" />
                <a
                  href={companyDetail?.companyWebsite}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:underline"
                >
                  {companyDetail?.companyWebsite}
                </a>
              </div>
            )}
            {companyDetail?.companyAddress && (
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span>{companyDetail?.companyAddress}</span>
              </div>
            )}
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span>Created on {companyDetail?.createdAt}</span>
            </div>
          </div>

          {/* Categories */}
          {companyDetail?.categories?.length > 0 && (
            <div>
              <h3 className="text-sm font-medium mb-2">Categories</h3>
              <div className="flex flex-wrap gap-2">
                {companyDetail?.categories.map((category) => (
                  <Badge key={category.categoryId} variant="secondary">
                    {category.name}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Contact Numbers */}
          <div className="grid gap-4">
            <ContactNumberCard
              title="Toll Free Numbers"
              numbers={companyDetail?.contactNumbers?.TOLL_FREE}
            />
            <ContactNumberCard
              title="All India Numbers"
              numbers={companyDetail?.contactNumbers?.ALL_INDIA}
            />
            <ContactNumberCard
              title="International Numbers"
              numbers={companyDetail?.contactNumbers?.INTERNATIONAL}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
