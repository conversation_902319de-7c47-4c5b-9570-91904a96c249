import { useEffect, useState } from 'react';
import { DashboardShell } from '@/components/dashboard-shell';
import DataTable from '@/components/data-table/data-table';
import { columns } from './companies-columns';
import usePagination from '@/hooks/use-pagination';
import { useCompanies } from '@/hooks/api/data.api';

export default function CompaniesPage() {
  const [data, setData] = useState([]);

  const {
    query,
    totalPages,
    setTotalPages,
    updateQuery,
    resetQuery,
    goToPage,
    setPageSize,
    sort,
    search,
  } = usePagination();

  const { getPaginatedCompanies, loading } = useCompanies();

  async function fetchCompanies() {
    const { companies, total } = await getPaginatedCompanies({
      ...query,
    }).catch((error) => {
      console.error('Failed to fetch companies:', error);
      return { categories: [] };
    });
    setData(companies);
    const pages = Math.ceil(total / query.limit);
    setTotalPages(pages);
  }

  useEffect(() => {
    fetchCompanies();
  }, [query]);

  const paginationProps = {
    query,
    updateQuery,
    resetQuery,
    goToPage,
    setPageSize,
    sort,
    search,
    totalPages,
  };
  return (
    <DataTable
      columns={columns}
      incomingData={data}
      isLoading={loading}
      title={'Companies'}
      description={'Manage your company records'}
      pagination={paginationProps}
    />
  );
}
