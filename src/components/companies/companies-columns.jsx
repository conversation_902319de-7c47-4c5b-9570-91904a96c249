import { Eye } from 'lucide-react';
import { Button } from '../ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '../ui/tooltip';
import { CompanyDetailsDialog } from './company-details-dialog';

export const columns = () => [
  {
    id: 'companyName',
    header: 'Company Name',
    cell: ({ row }) => row.original.companyName,
  },
  {
    id: 'parentCompany',
    header: 'Parent Company',
    cell: ({ row }) => row.original.parentCompany || '-',
  },
  {
    id: 'companyLogoUrl',
    header: 'Logo',
    cell: ({ row }) => {
      return row.original.companyLogoUrl ? (
        <img src={row.original.companyLogoUrl} className="max-h-8.5 w-auto" />
      ) : (
        '-'
      );
    },
  },
  {
    id: 'companyEmail',
    header: 'Company Email',
    cell: ({ row }) => row.original.companyEmail || '-',
  },
  {
    id: 'companyCountry',
    header: 'Country',
    cell: ({ row }) => row.original.companyCountry || '-',
  },
  {
    id: 'companyAddress',
    header: 'Address',
    cell: ({ row }) => row.original.companyAddress || '-',
  },
  {
    id: 'companyWebsite',
    header: 'Website',
    cell: ({ row }) =>
      row.original.companyWebsite ? (
        <Button variant={'link'}>
          <a href={row.original.companyWebsite} target="_blank">
            {row.original.companyWebsite}
          </a>
        </Button>
      ) : (
        '-'
      ),
  },
  {
    id: 'upVoteCount',
    header: 'Up Votes',
    cell: ({ row }) => row.original.upVoteCount || 0,
  },
  {
    id: 'downVoteCount',
    header: 'Down Votes',
    cell: ({ row }) => row.original.upVoteCount || 0,
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => {
      return (
        <Tooltip>
          <TooltipTrigger asChild>
            <CompanyDetailsDialog company={row.original} />
          </TooltipTrigger>
          <TooltipContent side="top" sideOffset={-4}>
            <p>View Details</p>
          </TooltipContent>
        </Tooltip>
      );
    },
  },
];
