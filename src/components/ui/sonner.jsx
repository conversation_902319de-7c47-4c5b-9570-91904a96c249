import { AlertCircle, CheckCircle, InfoIcon, XCircle } from "lucide-react";
import { useTheme } from "next-themes"
import { Toaster as Son<PERSON> } from "sonner";

const Toaster = ({
  ...props
}) => {
  const { theme = "system" } = useTheme()

  return (
    (<Sonner
      theme={theme}
      className="toaster group"
      style={
        {
          "--normal-bg": "var(--popover)",
          "--normal-text": "var(--popover-foreground)",
          "--normal-border": "var(--border)"
        }
      }
      icons={{
        success: <CheckCircle className="size-5 text-green-600" />,
        error: <XCircle className="size-5 text-red-600" />,
        warning: <AlertCircle className="size-5 text-yellow-600" />,
        info: <InfoIcon className="size-5 text-primary" />,
      }}
      {...props} />)
  );
}

export { Toaster }
