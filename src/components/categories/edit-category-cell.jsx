import { Pencil } from 'lucide-react';
import { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { toast } from 'sonner';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { useUpdateCategory } from '@/hooks/api/data.api';

// Updated validation schema
const schema = yup.object({
  categoryName: yup.string().required('Category name is required'),
  isActive: yup.boolean().required(),
});

export default function EditCategoryCell({ row, pagination }) {
  const [open, setOpen] = useState(false);

  const { updateCategoryFunc, loading } = useUpdateCategory();

  const defaultValues = useMemo(() => ({
    categoryName: row.original.name,
    isActive: row.original.isActive,
  }), [row.original.name, row.original.isActive]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues,
  });

  const { resetQuery } = pagination;

  useEffect(() => {
    if (open) {
      reset(defaultValues);
    }
  }, [open, defaultValues, reset]);

  const watchedCategoryName = watch('categoryName');
  const watchedStatus = watch('isActive');

  const isUnchanged =
    watchedCategoryName === row.original.name &&
    watchedStatus === row.original.isActive;

  const onSubmit = async (data) => {
    const payload = {
      ...data,
      categoryId: row.original.categoryId,
    };

    const res = await updateCategoryFunc(payload);

    if (res?.success) {
      toast.success(res.message);
      setOpen(false);
      resetQuery();
    }

    if (!res.success && res.error) {
      toast.error(res.error.message);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger
        className="h-5 w-5 inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-all cursor-pointer"
        asChild
      >
        <Pencil className="h-4 w-4" />
      </DialogTrigger>

      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Category</DialogTitle>
          <DialogDescription>
            Updates to do for <strong>{row.original.name}</strong> category.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-2">
            <label className="text-sm font-medium">Category Name</label>
            <Input {...register('categoryName')} />
            {errors.categoryName && (
              <p className="text-sm text-destructive">
                {errors.categoryName.message}
              </p>
            )}
          </div>

          <div className="mt-4 flex items-center gap-3">
            <label className="text-sm font-medium">Category Status</label>
            <Switch
              checked={watchedStatus}
              onCheckedChange={(value) =>
                setValue('isActive', value, { shouldValidate: true })
              }
            />
          </div>

          <DialogFooter className="mt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isUnchanged || loading}>
              {loading ? 'Saving...' : 'Save'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
