import { Tooltip, TooltipContent, TooltipTrigger } from '../ui/tooltip';
import EditCategoryCell from './edit-category-cell';
import UploadIconCell from './upload-icon-cell';

const getStatusBadgeClasses = (isActive) =>
  `inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
    isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
  }`;

export const columns = ({ paginationProps }) => {
  return [
    {
      id: 'categoryName',
      header: 'Name',
      cell: ({ row }) => row.original.name,
    },
    {
      id: 'iconUrl',
      header: 'Icon',
      cell: ({ row }) => {
        return row.original.iconUrl ? (
          <img
            src={row.original.iconUrl}
            className="w-auto max-h-8"
            alt={`${row.original.name} icon`}
          />
        ) : (
          '-'
        );
      },
    },
    {
      id: 'status',
      header: 'Status',
      cell: ({ row }) => (
        <span className={getStatusBadgeClasses(row.original.isActive)}>
          {row.original.isActive ? 'Active' : 'Inactive'}
        </span>
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        return (
          <div className="flex gap-2">
            <Tooltip>
              <TooltipTrigger>
                <UploadIconCell row={row} pagination={paginationProps} />
              </TooltipTrigger>
              <TooltipContent side="top" sideOffset={-4}>
                <p>Upload Icon</p>
              </TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger>
                <EditCategoryCell row={row} pagination={paginationProps} />
              </TooltipTrigger>
              <TooltipContent side="top" sideOffset={-4}>
                <p>Edit Category</p>
              </TooltipContent>
            </Tooltip>
          </div>
        );
      },
    },
  ];
};
