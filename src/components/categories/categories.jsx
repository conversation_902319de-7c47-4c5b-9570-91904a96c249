import { useEffect, useMemo, useState, memo } from 'react';
import DataTable from '@/components/data-table/data-table';
import { columns } from './categories-columns';
import { useCategories } from '@/hooks/api/data.api';
import usePagination from '@/hooks/use-pagination';
import { toast } from 'sonner';
// import { useRef } from 'react';

const CategoriesPage = () => {
  const [data, setData] = useState([]);
  // const previousQueryRef = useRef(null);

  const {
    query,
    totalPages,
    setTotalPages,
    updateQuery,
    resetQuery,
    goToPage,
    setPageSize,
    sort,
    search,
  } = usePagination();

  const { getPaginatedCategories, loading } = useCategories();

  // const fetchCategories = useCallback(async () => {
  //   const res = await getPaginatedCategories(query);
  //   if (res && !res?.success) {
  //     toast.error(res?.error || 'Failed to fetch categories');
  //     return;
  //   }
  //   const { categories, total = 0 } = res.data;
  //   setData(categories);
  //   const pages = Math.ceil(total / query.limit);
  //   setTotalPages(pages);
  // }, [query, setTotalPages, getPaginatedCategories]);

  async function fetchCategories() {
    const res = await getPaginatedCategories(query);
    if (res && !res?.success) {
      toast.error(res?.error || 'Failed to fetch categories');
      return;
    }
    const { categories, total = 0 } = res.data;
    setData(categories);
    const pages = Math.ceil(total / query.limit);
    setTotalPages(pages);
  }

  // const hasQueryChanged = useMemo(() => {
  //   const prevQuery = previousQueryRef.current;
  //   const currentQuery = JSON.stringify(query);
  //   return prevQuery !== currentQuery;
  // }, [query]);

  useEffect(() => {
    // if (hasQueryChanged) {
    //   previousQueryRef.current = JSON.stringify(query);
    // }
    fetchCategories();
  }, [query]);

  const paginationProps = useMemo(
    () => ({
      query,
      updateQuery,
      resetQuery,
      goToPage,
      setPageSize,
      sort,
      search,
      totalPages,
    }),
    [
      query,
      updateQuery,
      resetQuery,
      goToPage,
      setPageSize,
      sort,
      search,
      totalPages,
    ]
  );

  const memoizedColumns = useMemo(
    () => columns({ paginationProps }),
    [paginationProps]
  );

  return (
    <DataTable
      columns={memoizedColumns}
      incomingData={data}
      isLoading={loading}
      title="Categories"
      description="Manage your categories records"
      pagination={paginationProps}
    />
  );
};

CategoriesPage.displayName = 'CategoriesPage';
export default memo(CategoriesPage);
