import { Upload, X } from "lucide-react";
import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useUploadCategoryIcon } from '@/hooks/api/data.api';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { toast } from 'sonner';

const MAX_SIZE = 2 * 1024 * 1024;

// validation schema for the file upload
const fileSchema = yup.object({
  icon: yup
    .mixed()
    .required('Please select a file')
    .test('fileType', 'Only PNG, JPG, and JPEG files are allowed', (value) => {
      if (!value) return true;
      const validTypes = ['image/png', 'image/jpeg'];
      return validTypes.includes(value.type);
    })
    .test(
      'fileSize',
      `File size should not exceed ${MAX_SIZE / (1024 * 1024)} MB`,
      (value) => {
        if (!value) return true;
        return value.size <= MAX_SIZE;
      }
    ),
});

export default function UploadIconCell({ row, pagination }) {
  const [open, setOpen] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef(null);
  const { uploadIconFunc, error, loading } = useUploadCategoryIcon();

  const { resetQuery } = pagination;

  // Initialize react-hook-form with yup resolver
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm({
    resolver: yupResolver(fileSchema),
    defaultValues: {
      icon: null,
    },
  });

  // Watch the file value to display preview
  const fileValue = watch('icon');

  const onSubmit = async (data) => {
    if (!data.icon) return;

    const formData = new FormData();
    formData.append('icon', data.icon);
    formData.append('categoryId', row.original.categoryId);

    const res = await uploadIconFunc(formData);
    if (res?.success) {
      toast.success(res.message);
      setOpen(false);
      resetQuery();
    }
  };

  useEffect(() => {
    if (error) {
      toast.error(error);
    }
  }, [error]);

  const handleFileChange = (selectedFile) => {
    setValue('icon', selectedFile || null, { shouldValidate: true });
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFileChange(e.dataTransfer.files[0]);
    }
  };

  const clearFile = () => {
    reset();
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger
        className="h-5 w-5 inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-all cursor-pointer"
        asChild
      >
        <Upload className="h-4 w-4" />
        {/* <Button variant="ghost" size="icon" className="h-8 w-8">
        </Button> */}
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Upload Icon</DialogTitle>
          <DialogDescription>
            Upload a new icon for {row.original.name} category.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)}>
          {errors.icon && (
            <Alert variant="destructive">
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{errors.icon.message}</AlertDescription>
            </Alert>
          )}

          <div
            className={`mt-4 border-2 border-dashed rounded-lg p-6 text-center ${
              isDragging
                ? 'border-primary bg-primary/5'
                : errors.icon
                ? 'border-destructive/50'
                : 'border-muted-foreground/20'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <input
              type="file"
              accept="image/png,image/jpeg"
              className="hidden"
              ref={(e) => {
                register('icon').ref(e);
                fileInputRef.current = e;
              }}
              onChange={(e) => handleFileChange(e.target.files?.[0] || null)}
            />

            {!fileValue ? (
              <div className="space-y-4">
                <div className="flex justify-center">
                  <Upload className="h-10 w-10 text-muted-foreground" />
                </div>
                <div>
                  <p className="text-sm font-medium">
                    Click to browse or drag and drop
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Supported formats: PNG, JPG, JPEG
                  </p>
                </div>
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => fileInputRef.current?.click()}
                >
                  Select File
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <img
                      src={URL.createObjectURL(fileValue)}
                      alt="Preview"
                      className="h-8 w-8 object-contain"
                    />
                    <div className="text-left">
                      <p className="text-sm font-medium truncate max-w-[300px]">
                        {fileValue.name}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {(fileValue.size / 1024).toFixed(2)} KB
                      </p>
                    </div>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => handleFileChange(null)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </div>

          <DialogFooter className="mt-4">
            <Button type="button" variant="outline" onClick={clearFile}>
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!fileValue || !!errors.icon || loading}
            >
              {loading ? 'Uploading...' : 'Upload Icon'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}