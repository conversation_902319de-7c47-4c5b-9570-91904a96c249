import { getLocalStorageItem } from '@/lib/helpers/local-storage';
import { Navigate } from 'react-router';

function ProtectedRoute({ children }) {
  const isAuthenticated = Boolean(getLocalStorageItem('icc_tk'));
  return isAuthenticated ? children : <Navigate to="/" replace />;
}

function PublicRoute({ children }) {
  const isAuthenticated = Boolean(getLocalStorageItem('icc_tk'));
  return !isAuthenticated ? children : <Navigate to="/dashboard" replace />;
}

export { ProtectedRoute, PublicRoute };
export default ProtectedRoute;
