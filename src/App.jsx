import { Route, Routes } from "react-router";
import "./App.css";
import { ProtectedRoute, PublicRoute } from '@/components/protected-routes';
import { routes } from '@/routes/routes';
import { AuthProvider } from './context/auth-context';
import { Toaster } from '@/components/ui/sonner';

function App() {
  return (
    <AuthProvider>
      <Routes>
        {routes.map((route, index) => {
          return (
            <Route
              key={index}
              path={route.path}
              element={
                route.isProtected && route.isPublic ? (
                  route.element
                ) : route.isProtected ? (
                  <ProtectedRoute>{route.element}</ProtectedRoute>
                ) : route.isPublic ? (
                  <PublicRoute>{route.element}</PublicRoute>
                ) : (
                  route.element
                )
              }
            />
          );
        })}
      </Routes>
      <Toaster
        swipeDirections={'right'}
        position={'top-right'}
        closeButton={true}
      />
    </AuthProvider>
  );
}

export default App;
