import LoginForm from "@/pages/login-form";
import ForgotPasswordPage from "@/pages/forgot-password-page";
import Dashboard from "@/pages/dashboard";
import CompaniesPage from "@/components/companies/companies";
import CategoriesPage from "@/components/categories/categories";
import ContactNumbersPage from "@/components/contact-numbers/contact-numbers";

export const routes = [
  {
    path: '/',
    element: <LoginForm />,
    isProtected: false,
    isPublic: true,
  },
  {
    path: '/forgot-password',
    element: <ForgotPasswordPage />,
    isProtected: false,
    isPublic: true,
  },
  {
    path: '/dashboard/*',
    element: <Dashboard />,
    isProtected: true,
  },
];
