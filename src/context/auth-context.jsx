import { createContext, useState } from 'react';
import { getLocalStorageItem } from '@/lib/helpers/local-storage';

const AuthContext = createContext(null);

export function AuthProvider({ children }) {
  const [user, setUser] = useState(() => {
    const token = getLocalStorageItem('icc_tk');
    const storedUser = getLocalStorageItem('user');
    return token && storedUser ? JSON.parse(storedUser) : null;
  });

  const value = {
    user,
    setUser,
    isAuthenticated: !!user,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export { AuthContext };
