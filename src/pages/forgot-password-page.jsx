import { useState } from 'react';
import { useNavigate } from 'react-router';
import { BeatLoader } from 'react-spinners';
import * as Yup from 'yup';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
// import useFetch from '@/hooks/use-fetch';
// import { login } from '@/api/auth.api';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { LogoDarkVertical } from '@/assets';
import {
  useForgotPassword,
  useResetPassword,
  useVerifyOtp,
} from '@/hooks/api/auth.api';
import { toast } from 'sonner';

function ForgotPassword({ onSuccess }) {
  const { forgotPasswordFunc, _error, loading } = useForgotPassword();

  const validationSchema = Yup.object().shape({
    email: Yup.string().email('Invalid email').required('Email is required'),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(validationSchema),
  });

  async function handleForgotPassword(formData) {
    const res = await forgotPasswordFunc(formData);
    if (res) {
      if (res.success) {
        toast.success(res.message);
        onSuccess(formData.email);
      } else if (res.success === false) {
        toast.error(res.error);
      }
    }
  }

  return (
    <form onSubmit={handleSubmit(handleForgotPassword)}>
      <div className="flex flex-col gap-5">
        <div className="grid gap-1">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            {...register('email')}
          />
          {errors?.email && (
            <p className="text-red-500 text-sm">{errors.email.message}</p>
          )}
        </div>
        <Button
          type="submit"
          variant={'default'}
          disabled={loading}
          className="w-full"
        >
          {loading ? <BeatLoader size={10} color="#36d7b7" /> : 'Send OTP'}
        </Button>
      </div>
    </form>
  );
}

function OTPInput({ email, onSuccess }) {
  const validationSchema = Yup.object().shape({
    otp: Yup.string().required('OTP is required').min(6, 'Invalid OTP'),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(validationSchema),
  });

  const { verifyOtp, loading: verifyOtpLoading } = useVerifyOtp();

  const handleVerifyOtp = async (formData) => {
    const response = await verifyOtp({ email, otp: formData.otp });
    if (response && response.success) {
      toast.success(response.message);
      onSuccess();
    } else if (response && response.success === false) {
      toast.error(response.error);
    }
  };

  const { forgotPasswordFunc } = useForgotPassword();

  async function handleResendOTP() {
    const res = await forgotPasswordFunc({ email });
    if (res) {
      if (res.success) {
        toast.success(res.message);
      } else if (res.success === false) {
        toast.error(res.error);
      }
    }
  }

  return (
    <form onSubmit={handleSubmit(handleVerifyOtp)}>
      <div className="flex flex-col gap-5">
        <div className="grid gap-1">
          <Label htmlFor="otp">Enter OTP</Label>
          <Input
            id="otp"
            type="text"
            placeholder="Enter OTP"
            {...register('otp')}
          />
          {errors?.otp && (
            <p className="text-red-500 text-sm">{errors.otp.message}</p>
          )}
        </div>

        <div className="flex items-center justify-end -mb-3 -mt-2">
          <Button
            variant={'ghost'}
            type="button"
            onClick={handleResendOTP}
            className="text-sm text-primary hover:underline hover:text-primary hover:font-bold p-0"
          >
            Resend OTP
          </Button>
        </div>

        <Button
          type="submit"
          variant={'default'}
          disabled={verifyOtpLoading}
          className="w-full"
        >
          {verifyOtpLoading ? (
            <BeatLoader size={10} color="#36d7b7" />
          ) : (
            'Verify OTP'
          )}
        </Button>
      </div>
    </form>
  );
}

function ResetPassword({ email, onSuccess }) {
  const validationSchema = Yup.object().shape({
    newPassword: Yup.string()
      .required('New password is required')
      .min(6, 'Password must be at least 6 characters'),
    // .matches(
    //   /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    //   'Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character'
    // ),
    confirmPassword: Yup.string()
      .required('Confirm password is required')
      .oneOf([Yup.ref('newPassword')], 'Passwords must match'),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(validationSchema),
  });

  const { resetPassword, loading } = useResetPassword();

  const handleResetPassword = async (formData) => {
    const res = await resetPassword({
      email,
      password: formData.newPassword,
      confirmPassword: formData.confirmPassword,
    });
    if (res) {
      if (res.success) {
        toast.success(res.message);
        onSuccess();
      } else if (res.success === false) {
        toast.error(res.error);
      }
    }
  };

  return (
    <form onSubmit={handleSubmit(handleResetPassword)}>
      <div className="flex flex-col gap-5">
        <div className="grid gap-1">
          <Label htmlFor="newPassword">New Password</Label>
          <Input
            id="newPassword"
            type="password"
            {...register('newPassword')}
          />
          {errors?.newPassword && (
            <p className="text-red-500 text-sm">{errors.newPassword.message}</p>
          )}
        </div>
        <div className="grid gap-1">
          <Label htmlFor="confirmPassword">Confirm Password</Label>
          <Input
            id="confirmPassword"
            type="password"
            {...register('confirmPassword')}
          />
          {errors?.confirmPassword && (
            <p className="text-red-500 text-sm">
              {errors.confirmPassword.message}
            </p>
          )}
        </div>
        <Button
          type="submit"
          variant={'default'}
          disabled={loading}
          className="w-full"
        >
          {loading ? (
            <BeatLoader size={10} color="#36d7b7" />
          ) : (
            'Reset Password'
          )}
        </Button>
      </div>
    </form>
  );
}

export default function ForgotPasswordPage() {
  const [step, setStep] = useState(1);
  const [email, setEmail] = useState('');
  const navigate = useNavigate();

  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10 bg-primary">
      <div className="w-full max-w-sm">
        <Card>
          <CardHeader>
            <div className="w-full flex items-center justify-center mb-2">
              <LogoDarkVertical height={'4.5rem'} width={'auto'} />
            </div>
            <CardTitle className="text-2xl text-center">
              {step === 1
                ? 'Enter Your Email'
                : step === 2
                ? 'Enter OTP'
                : 'Reset Password'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {step === 1 && (
              <ForgotPassword
                onSuccess={(email) => {
                  setEmail(email);
                  setStep(2);
                }}
              />
            )}
            {step === 2 && (
              <OTPInput email={email} onSuccess={() => setStep(3)} />
            )}
            {step === 3 && (
              <ResetPassword email={email} onSuccess={() => navigate('/')} />
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
