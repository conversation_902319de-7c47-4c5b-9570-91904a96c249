import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router';
import { <PERSON><PERSON>oader } from 'react-spinners';
import * as Yup from 'yup';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { LogoDarkVertical } from '@/assets';
import { useLogin } from '@/hooks/api/auth.api';
import { toast } from 'sonner';
import { Eye, EyeOff } from 'lucide-react';

const validationSchema = Yup.object().shape({
  email: Yup.string().email('Invalid email').required('Email is required'),
  password: Yup.string().required('Password is required'),
});

export default function LoginForm() {
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);

  const { login, loading, error } = useLogin();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(validationSchema),
  });

  async function handleLogin(formData) {
    const res = await login(formData);
    if (res.success) {
      navigate('/dashboard/categories');
      toast.success(res.message);
    }
  }

  useEffect(() => {
    if (error) {
      toast.error(error);
    }
  }, [error]);

  const togglePasswordVisibility = () => setShowPassword((prev) => !prev);

  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10 bg-primary">
      <div className="w-full max-w-sm">
        <Card>
          <CardHeader>
            <div className="w-full flex items-center justify-center mb-2">
              <LogoDarkVertical height={'4.5rem'} width={'auto'} />
            </div>
            <CardTitle className="text-2xl text-center">
              Login to your Account
            </CardTitle>
            {/* <CardDescription>
              Enter your email and password to continue.
            </CardDescription> */}
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(handleLogin)}>
              <div className="flex flex-col gap-5">
                <div className="grid gap-1">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    {...register('email')}
                  />
                  {errors?.email && (
                    <p className="text-red-500 text-sm">
                      {errors.email.message}
                    </p>
                  )}
                </div>

                <div className="grid gap-1 relative">
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    {...register('password')}
                    className="pr-10"
                  />
                  <button
                    type="button"
                    onClick={togglePasswordVisibility}
                    className="absolute right-3 top-6.5 text-primary hover:text-foreground focus:outline-none cursor-pointer"
                    tabIndex={-1}
                  >
                    {showPassword ? (
                      <Eye width={'auto'} height={'1.35rem'} />
                    ) : (
                      <EyeOff width={'auto'} height={'1.35rem'} />
                    )}
                  </button>
                  {errors?.password && (
                    <p className="text-red-500 text-sm">
                      {errors.password.message}
                    </p>
                  )}
                </div>

                <div className="flex items-center justify-end -mb-3 -mt-2">
                  <Button
                    variant={'ghost'}
                    type="button"
                    onClick={() => navigate('/forgot-password')}
                    className="text-sm text-primary hover:underline hover:text-primary hover:font-bold p-0"
                  >
                    Forgot password?
                  </Button>
                </div>

                <Button
                  type="submit"
                  variant={'default'}
                  disabled={loading}
                  className="w-full"
                >
                  {loading ? <BeatLoader size={10} color="#36d7b7" /> : 'Login'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
