import React from 'react';
import { Navigate, Route, Routes, useLocation } from 'react-router';
import { DashboardShell } from '@/components/dashboard-shell';
import CompaniesPage from '@/components/companies/companies';
import CategoriesPage from '@/components/categories/categories';
import StaticContentPage from '@/components/static-content/static-content';
import { SidebarProvider } from '@/components/ui/sidebar';
import { TooltipProvider } from '@/components/ui/tooltip';

export default function Dashboard() {
  const location = useLocation();
  const path = location.pathname.split('/').pop();

  const getBreadcrumbs = () => {
    const base = [{ label: 'Dashboard', href: '/dashboard' }];
    if (path === 'companies') {
      return [...base, { label: 'Companies', href: '/dashboard/companies' }];
    } else if (path === 'categories') {
      return [...base, { label: 'Categories', href: '/dashboard/categories' }];
    } else if (path === 'static') {
      return [...base, { label: 'Static Content', href: '/dashboard/static' }];
    }
    return base;
  };

  return (
    <TooltipProvider>
      <SidebarProvider>
        <DashboardShell breadcrumbs={getBreadcrumbs()}>
          <Routes>
            <Route path="" element={<Navigate to="categories" replace />} />
            <Route path="companies" element={<CompaniesPage />} />
            <Route path="categories" element={<CategoriesPage />} />
            <Route path="static" element={<StaticContentPage />} />
          </Routes>
        </DashboardShell>
      </SidebarProvider>
    </TooltipProvider>
  );
}
