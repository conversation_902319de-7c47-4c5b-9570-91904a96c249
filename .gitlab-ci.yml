include:
  remote: 'https://${CI_SERVER_HOST}/public-resources/gitlab-ci/-/raw/master/templates/build.yaml'
stages:
  - build
  - deploy
variables:
  NGINX_PATH: "/home/<USER>/docker-stack/nginx_conf/react"
  PROJECT: "india-customer-care-cms" 
build: 
  stage: build
  image:
    name: node:20-alpine
  extends: .build_static
  script:
    - cp ${ENV_FILE:-.env.$CI_COMMIT_REF_SLUG} .env || ls -la
    - rm -rf .env.*
    - npm install
    - npm run build
  artifacts:
    paths: 
      - dist/*

deploy_dev:
  stage: deploy
  extends: .deploy_static
  variables:
    BUILD_PATH: dist
  only:
    - development
