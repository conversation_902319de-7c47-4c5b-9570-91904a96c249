{"name": "my-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.8", "@tailwindcss/vite": "^4.0.15", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jodit-react": "^5.2.19", "lucide-react": "^0.483.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-router": "^7.4.0", "react-spinners": "^0.15.0", "sonner": "^2.0.3", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.15", "tw-animate-css": "^1.2.4", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.21.0", "@svgr/rollup": "^8.1.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "vite": "^6.2.0"}}