# CMS Customer Care Portal

A React-based customer care management system with authentication and dashboard features.

## Tech Stack

- React 19
- Vite 6
- Tai<PERSON>windCSS
- Radix UI Components
- React Router
- React Hook Form + Yup
- Lucide Icons

## Getting Started

1. Clone the repository
2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Build for production:
```bash
npm run build
```

## Project Structure

```
└── 📁cms-customer-care
    └── 📁public
        └── vite.svg
    └── 📁src
        └── App.css
        └── App.jsx
        └── 📁assets
        └── 📁components
            └── 📁ui
        └── 📁db
        └── 📁hooks
        └── index.css
        └── 📁lib
        └── main.jsx
        └── 📁pages
    └── index.html
```

## Environment Setup

The project requires a backend API running on port 3000. Make sure to set up the following environment variables:

```env
VITE_API_URL=http://localhost:3000
```
